import React, { useEffect } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import EventLandingTemplate from './EventLandingTemplate'
import KidsCampParentSection from '@/components/KidsCampParentSection'
import { aiCodingKidsCampConfig } from '@/data/events/ai-coding-kids-camp'

gsap.registerPlugin(ScrollTrigger)

const AICodingKidsCamp: React.FC = () => {
  useEffect(() => {
    // Set up GSAP defaults
    gsap.config({
      nullTargetWarn: false
    })

    // Clean up on unmount
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  // Update page title and meta description
  useEffect(() => {
    document.title = 'AI + Coding Kids Camp - Virtual Learning Adventure | Kavara Digital'
    
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', 
        'Join our exciting virtual AI + Coding Kids Camp for children aged 8-14. Learn programming fundamentals and AI basics in a safe, fun environment. August 11-22, 2025.'
      )
    }

    // Add structured data for the event
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Event",
      "name": "AI + Coding Kids Camp",
      "description": "Virtual AI and coding camp for children aged 8-14",
      "startDate": "2025-08-11T15:00:00+01:00",
      "endDate": "2025-08-22T16:00:00+01:00",
      "eventStatus": "https://schema.org/EventScheduled",
      "eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
      "location": {
        "@type": "VirtualLocation",
        "url": "https://kavaradigital.online/ai-coding-kids-camp"
      },
      "organizer": {
        "@type": "Organization",
        "name": "Kavara Digital",
        "url": "https://kavaradigital.online"
      },
      "offers": {
        "@type": "Offer",
        "price": "50000",
        "priceCurrency": "NGN",
        "availability": "https://schema.org/InStock",
        "validFrom": "2025-01-01T00:00:00+01:00"
      },
      "audience": {
        "@type": "Audience",
        "audienceType": "Children aged 8-14"
      }
    }

    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.textContent = JSON.stringify(structuredData)
    document.head.appendChild(script)

    return () => {
      document.title = 'Kavara Digital - Premium Web Development & Digital Marketing Agency'
      if (script.parentNode) {
        script.parentNode.removeChild(script)
      }
    }
  }, [])

  return (
    <div>
      <EventLandingTemplate config={aiCodingKidsCampConfig} />
      <KidsCampParentSection
        contact={aiCodingKidsCampConfig.contact}
        registrationUrl={aiCodingKidsCampConfig.registrationUrl}
      />
    </div>
  )
}

export default AICodingKidsCamp
