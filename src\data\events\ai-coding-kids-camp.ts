import { EventConfig } from '@/types/event'

export const aiCodingKidsCampConfig: EventConfig = {
  // Basic Info
  id: 'ai-coding-kids-camp',
  title: 'AI + Coding Kids Camp',
  subtitle: 'Virtual Learning Adventure for Young Minds',
  description: 'Join our exciting virtual AI + Coding Kids Camp where children aged 8-14 learn programming fundamentals while exploring the fascinating world of Artificial Intelligence in a safe, fun, and engaging environment.',
  type: 'training',
  
  // Event Details
  details: {
    date: 'August 11-22, 2025',
    time: '3:00 PM - 4:00 PM',
    duration: '1 hour daily sessions',
    location: 'Virtual (Online)',
    format: 'Interactive Virtual Sessions',
    capacity: 25,
    language: 'English',
    requirements: [
      'Computer or tablet with internet connection',
      'Basic reading and writing skills',
      'Enthusiasm to learn!',
      'Parent/guardian supervision recommended for younger kids'
    ]
  },
  
  // Pricing
  pricing: {
    earlyBird: {
      price: 50000,
      currency: '₦',
      deadline: 'August 5th',
      savings: '₦30,000 savings!'
    },
    regular: {
      price: 80000,
      currency: '₦'
    }
  },
  
  // Content
  benefits: [
    {
      icon: '🧠',
      title: 'Critical Thinking Skills',
      description: 'Develop problem-solving abilities and logical reasoning through hands-on coding challenges'
    },
    {
      icon: '🤖',
      title: 'AI Fundamentals',
      description: 'Learn the basics of Artificial Intelligence in kid-friendly, interactive ways'
    },
    {
      icon: '💻',
      title: 'Programming Basics',
      description: 'Master fundamental coding concepts using visual programming tools and simple languages'
    },
    {
      icon: '🎯',
      title: 'Future-Ready Skills',
      description: 'Build essential digital literacy skills for the technology-driven future'
    },
    {
      icon: '👥',
      title: 'Safe Learning Environment',
      description: 'Small group sizes with expert instructors ensuring personalized attention'
    },
    {
      icon: '🏠',
      title: 'Learn from Home',
      description: 'Convenient virtual format - no travel required, learn safely from home'
    }
  ],
  
  testimonials: [
    {
      name: 'Mrs. Sarah Adebayo',
      role: 'Parent',
      content: 'My 10-year-old daughter absolutely loved the camp! She went from knowing nothing about coding to building her own simple games. The instructors were patient and made learning so much fun.',
      rating: 5,
      image: 'https://api.dicebear.com/7.x/initials/svg?seed=SA'
    },
    {
      name: 'Mr. David Okafor',
      role: 'Parent',
      content: 'This camp exceeded our expectations. My twin boys (age 12) are now constantly talking about AI and want to learn more programming. Great investment in their future!',
      rating: 5,
      image: 'https://api.dicebear.com/7.x/initials/svg?seed=DO'
    },
    {
      name: 'Dr. Funmi Oluwaseun',
      role: 'Education Specialist',
      content: 'As an educator, I\'m impressed by the curriculum design. It perfectly balances fun with learning, making complex concepts accessible to young minds.',
      rating: 5,
      image: 'https://api.dicebear.com/7.x/initials/svg?seed=FO'
    }
  ],
  
  // Detailed Agenda
  agenda: [
    {
      day: 'Week 1: Programming Foundations',
      sessions: [
        {
          time: 'Day 1-2',
          title: 'Introduction to Coding',
          description: 'What is programming? Visual coding with Scratch basics'
        },
        {
          time: 'Day 3-4',
          title: 'Creating Your First Game',
          description: 'Build simple interactive games and animations'
        },
        {
          time: 'Day 5-6',
          title: 'Logic and Problem Solving',
          description: 'Coding puzzles and logical thinking challenges'
        }
      ]
    },
    {
      day: 'Week 2: AI Adventures',
      sessions: [
        {
          time: 'Day 7-8',
          title: 'What is AI?',
          description: 'Understanding Artificial Intelligence through fun examples'
        },
        {
          time: 'Day 9-10',
          title: 'AI in Everyday Life',
          description: 'Exploring how AI helps us in games, apps, and daily activities'
        },
        {
          time: 'Day 11-12',
          title: 'Build Your AI Project',
          description: 'Create a simple AI-powered project and showcase to parents'
        }
      ]
    }
  ],
  
  // Marketing
  urgencyMessage: 'Only 25 spots available! Early bird pricing ends August 5th',
  spotsRemaining: 12,
  totalSpots: 25,
  
  // Contact & Registration
  contact: {
    phone: '0802-902-0121',
    email: '<EMAIL>',
    whatsapp: {
      number: '2348029020121',
      message: 'Hi! I want to register my child for the AI + Coding Kids Camp'
    }
  },
  registrationUrl: 'https://forms.gle/KidsAICodingCamp2025',
  
  // Customization
  theme: {
    primaryColor: '#FF6B9D',
    accentColor: '#4ECDC4',
    backgroundImage: '/kids-coding-bg.jpg'
  },

  // SEO
  seo: {
    title: 'AI + Coding Kids Camp - Virtual Learning Adventure | Kavara Digital',
    description: 'Join our exciting virtual AI + Coding Kids Camp for children aged 8-14. Learn programming fundamentals and AI basics in a safe, fun environment. August 11-22, 2025.',
    keywords: ['kids coding camp', 'AI for kids', 'programming for children', 'virtual coding camp', 'STEM education', 'Nigeria kids coding'],
    ogImage: '/kids-coding-og.jpg'
  },

  // Features
  features: {
    countdown: true,
    testimonials: true,
    speakers: false,
    agenda: true,
    pricing: true,
    registration: true
  }
}
