import React, { useEffect } from 'react'
import { gsap } from 'gsap'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Clock, 
  Users, 
  Shield, 
  Star, 
  Phone, 
  Mail,
  MessageCircle,
  CheckCircle,
  Heart,
  Award,
  Home,
  Calendar
} from 'lucide-react'

interface KidsCampParentSectionProps {
  contact: {
    phone: string
    email: string
    whatsapp: {
      number: string
      message: string
    }
  }
  registrationUrl: string
}

const KidsCampParentSection: React.FC<KidsCampParentSectionProps> = ({
  contact,
  registrationUrl
}) => {
  useEffect(() => {
    gsap.fromTo('.parent-section-card',
      { y: 30, opacity: 0 },
      { 
        y: 0, 
        opacity: 1, 
        duration: 0.8, 
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.parent-section',
          start: 'top 80%'
        }
      }
    )
  }, [])

  const parentBenefits = [
    {
      icon: <Award className="h-6 w-6 text-pink-400" />,
      title: 'Builds Critical Thinking Skills',
      description: 'Develops problem-solving abilities and logical reasoning through hands-on coding challenges'
    },
    {
      icon: <Shield className="h-6 w-6 text-green-400" />,
      title: 'Safe & Supportive Environment',
      description: 'Small group sizes with expert instructors ensuring personalized attention and safety'
    },
    {
      icon: <Star className="h-6 w-6 text-yellow-400" />,
      title: 'Future-Ready Skills',
      description: 'Builds essential digital literacy and STEM skills for the technology-driven future'
    },
    {
      icon: <Home className="h-6 w-6 text-blue-400" />,
      title: 'Learn from Home',
      description: 'Convenient virtual format - no travel required, learn safely from home'
    }
  ]

  const scheduleDetails = [
    {
      icon: <Calendar className="h-5 w-5 text-pink-400" />,
      label: 'Duration',
      value: '12 Days (August 11-22)'
    },
    {
      icon: <Clock className="h-5 w-5 text-blue-400" />,
      label: 'Daily Sessions',
      value: '1 hour (3:00 PM - 4:00 PM)'
    },
    {
      icon: <Users className="h-5 w-5 text-green-400" />,
      label: 'Age Group',
      value: 'Kids aged 8-14 years'
    },
    {
      icon: <Shield className="h-5 w-5 text-purple-400" />,
      label: 'Class Size',
      value: 'Small groups (max 25 kids)'
    }
  ]

  const parentTestimonials = [
    {
      name: 'Mrs. Adunni Bakare',
      role: 'Mother of 2',
      quote: 'My kids are now constantly talking about AI and coding! They\'ve gained so much confidence and problem-solving skills.',
      rating: 5
    },
    {
      name: 'Mr. Chidi Okonkwo',
      role: 'Father',
      quote: 'Best investment in my daughter\'s future. She went from being scared of computers to building her own games!',
      rating: 5
    }
  ]

  return (
    <section className="parent-section py-20 bg-gradient-to-br from-pink-50 to-purple-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16 parent-section-card">
          <Badge className="bg-pink-100 text-pink-600 border-pink-200 mb-4">
            For Parents
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Why Parents Love Our Camp
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Give your child the gift of future-ready skills in a safe, engaging, and fun virtual environment
          </p>
        </div>

        {/* Parent Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {parentBenefits.map((benefit, index) => (
            <Card key={index} className="parent-section-card bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-br from-pink-100 to-purple-100 rounded-full">
                    {benefit.icon}
                  </div>
                  <CardTitle className="text-gray-800">{benefit.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{benefit.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Schedule & Details */}
        <Card className="parent-section-card bg-white/90 backdrop-blur-sm border-0 shadow-xl mb-16">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-gray-800 flex items-center justify-center gap-2">
              <Clock className="h-6 w-6 text-pink-400" />
              Schedule & Time Commitment
            </CardTitle>
            <CardDescription className="text-gray-600">
              Flexible timing designed to fit your family's schedule
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {scheduleDetails.map((detail, index) => (
                <div key={index} className="text-center p-4 bg-gradient-to-br from-pink-50 to-purple-50 rounded-lg">
                  <div className="flex justify-center mb-3">
                    {detail.icon}
                  </div>
                  <h4 className="font-semibold text-gray-800 mb-1">{detail.label}</h4>
                  <p className="text-gray-600 text-sm">{detail.value}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Parent Testimonials */}
        <div className="parent-section-card mb-16">
          <h3 className="text-3xl font-bold text-center text-gray-800 mb-8">
            What Parents Are Saying
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {parentTestimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/90 backdrop-blur-sm border-0 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 italic">"{testimonial.quote}"</p>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full flex items-center justify-center">
                      <Heart className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-800">{testimonial.name}</p>
                      <p className="text-sm text-gray-600">{testimonial.role}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Contact & Registration */}
        <Card className="parent-section-card bg-gradient-to-r from-pink-500 to-purple-600 text-white border-0 shadow-2xl">
          <CardContent className="p-8 text-center">
            <h3 className="text-3xl font-bold mb-4">Ready to Secure Your Child's Spot?</h3>
            <p className="text-pink-100 mb-8 text-lg">
              Have questions? We're here to help! Contact us directly or register now.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <a 
                href={`tel:${contact.phone}`}
                className="flex items-center justify-center gap-3 p-4 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
              >
                <Phone className="h-5 w-5" />
                <span>{contact.phone}</span>
              </a>
              
              <a 
                href={`mailto:${contact.email}`}
                className="flex items-center justify-center gap-3 p-4 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
              >
                <Mail className="h-5 w-5" />
                <span>Email Us</span>
              </a>
              
              <a 
                href={`https://wa.me/${contact.whatsapp.number}?text=${encodeURIComponent(contact.whatsapp.message)}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-3 p-4 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
              >
                <MessageCircle className="h-5 w-5" />
                <span>WhatsApp</span>
              </a>
            </div>

            <Button 
              size="lg" 
              className="bg-white text-pink-600 hover:bg-pink-50 font-bold px-8 py-4 text-lg"
              onClick={() => window.open(registrationUrl, '_blank')}
            >
              <CheckCircle className="mr-2 h-5 w-5" />
              Register Your Child Now
            </Button>
          </CardContent>
        </Card>
      </div>
    </section>
  )
}

export default KidsCampParentSection
