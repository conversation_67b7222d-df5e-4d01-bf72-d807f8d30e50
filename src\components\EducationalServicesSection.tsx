import React, { useEffect } from 'react'
import { gsap } from 'gsap'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  GraduationCap, 
  Users, 
  Clock, 
  Star,
  ArrowRight,
  Calendar,
  MapPin,
  Sparkles
} from 'lucide-react'

const EducationalServicesSection: React.FC = () => {
  useEffect(() => {
    gsap.fromTo('.education-card',
      { y: 30, opacity: 0 },
      { 
        y: 0, 
        opacity: 1, 
        duration: 0.8, 
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.education-section',
          start: 'top 80%'
        }
      }
    )
  }, [])

  const educationalServices = [
    {
      id: 'ai-coding-kids-camp',
      title: 'AI + Coding Kids Camp',
      subtitle: 'Virtual Learning Adventure for Young Minds',
      description: 'Interactive virtual camp where children aged 8-14 learn programming fundamentals while exploring AI in a safe, fun environment.',
      duration: '12 Days',
      dates: 'August 11-22, 2025',
      time: '3:00 PM - 4:00 PM',
      ageGroup: '8-14 years',
      format: 'Virtual',
      earlyBirdPrice: '₦50,000',
      regularPrice: '₦80,000',
      savings: '₦30,000',
      features: [
        'Learn Programming Basics',
        'AI Fundamentals for Kids',
        'Interactive Virtual Sessions',
        'Small Group Learning',
        'Safe Learning Environment',
        'Certificate of Completion'
      ],
      highlights: [
        'Builds Critical Thinking',
        'Future-Ready Skills',
        'Expert Instructors',
        'Parent-Friendly Schedule'
      ],
      cta: 'Register Your Child',
      link: '/ai-coding-kids-camp',
      new: true,
      popular: false
    },
    {
      id: 'code-ai-workshop',
      title: 'Code & AI Workshop',
      subtitle: 'Professional Development for Adults',
      description: 'Intensive workshop for professionals looking to upskill in coding and AI technologies.',
      duration: '1 Day',
      dates: 'August 23, 2025',
      time: '9:00 AM - 4:00 PM',
      ageGroup: 'Adults',
      format: 'Hybrid',
      earlyBirdPrice: 'Free',
      regularPrice: 'Free',
      savings: '',
      features: [
        'Hands-on Coding Experience',
        'AI Tools & Applications',
        'Industry Best Practices',
        'Networking Opportunities',
        'Certificate of Attendance',
        'Resource Materials'
      ],
      highlights: [
        'Industry Experts',
        'Practical Projects',
        'Career Advancement',
        'Free Attendance'
      ],
      cta: 'Join Workshop',
      link: '/code-and-ai-workshop',
      new: false,
      popular: true
    }
  ]

  return (
    <section className="education-section py-20 bg-gradient-to-br from-purple-900/20 to-blue-900/20">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16 education-card">
          <Badge className="bg-purple-100 text-purple-600 border-purple-200 mb-4">
            <GraduationCap className="mr-2 h-4 w-4" />
            Educational Services
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Learn & Grow with <span className="text-agency-green">Kavara Digital</span>
          </h2>
          <p className="text-xl text-agency-white-muted max-w-3xl mx-auto">
            Empower yourself and your children with cutting-edge technology skills through our specialized training programs
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {educationalServices.map((service) => (
            <Card 
              key={service.id} 
              className={`education-card bg-agency-darker/50 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 ${
                service.popular ? 'ring-2 ring-agency-green' : ''
              }`}
            >
              {/* Card Header */}
              <CardHeader className="relative">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex gap-2">
                    {service.new && (
                      <Badge className="bg-green-500/10 text-green-400 border-green-500/20">
                        <Sparkles className="mr-1 h-3 w-3" />
                        New
                      </Badge>
                    )}
                    {service.popular && (
                      <Badge className="bg-agency-green/10 text-agency-green border-agency-green/20">
                        <Star className="mr-1 h-3 w-3" />
                        Popular
                      </Badge>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-agency-green">
                      {service.earlyBirdPrice}
                    </div>
                    {service.savings && (
                      <div className="text-sm text-agency-white-muted line-through">
                        {service.regularPrice}
                      </div>
                    )}
                  </div>
                </div>

                <CardTitle className="text-white text-2xl mb-2">
                  {service.title}
                </CardTitle>
                <CardDescription className="text-agency-white-muted text-lg">
                  {service.subtitle}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Description */}
                <p className="text-agency-white-muted">
                  {service.description}
                </p>

                {/* Event Details */}
                <div className="grid grid-cols-2 gap-4 p-4 bg-agency-dark/50 rounded-lg">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-agency-green" />
                    <span className="text-agency-white-muted">{service.dates}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-agency-green" />
                    <span className="text-agency-white-muted">{service.time}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-agency-green" />
                    <span className="text-agency-white-muted">{service.ageGroup}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-agency-green" />
                    <span className="text-agency-white-muted">{service.format}</span>
                  </div>
                </div>

                {/* Key Highlights */}
                <div>
                  <h4 className="text-white font-semibold mb-3">Key Highlights:</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {service.highlights.map((highlight, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-agency-green rounded-full"></div>
                        <span className="text-agency-white-muted">{highlight}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Features */}
                <div>
                  <h4 className="text-white font-semibold mb-3">What You'll Get:</h4>
                  <div className="space-y-2">
                    {service.features.slice(0, 4).map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-agency-green rounded-full"></div>
                        <span className="text-agency-white-muted">{feature}</span>
                      </div>
                    ))}
                    {service.features.length > 4 && (
                      <div className="text-xs text-agency-white-muted">
                        +{service.features.length - 4} more benefits
                      </div>
                    )}
                  </div>
                </div>

                {/* Savings Badge */}
                {service.savings && (
                  <div className="bg-gradient-to-r from-green-500/10 to-agency-green/10 border border-green-500/20 rounded-lg p-3 text-center">
                    <div className="text-green-400 font-semibold">
                      Save {service.savings} with Early Bird!
                    </div>
                    <div className="text-xs text-agency-white-muted">
                      Limited time offer
                    </div>
                  </div>
                )}

                {/* CTA Button */}
                <Button 
                  className={`w-full py-6 text-lg font-semibold transition-all ${
                    service.popular
                      ? 'bg-agency-green text-agency-dark hover:bg-agency-green/90'
                      : 'bg-transparent border-2 border-agency-green text-agency-green hover:bg-agency-green hover:text-agency-dark'
                  }`}
                  onClick={() => window.open(service.link, '_blank')}
                >
                  {service.cta}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16 education-card">
          <div className="bg-agency-darker/50 backdrop-blur-sm rounded-xl p-8 border border-agency-green/20">
            <h3 className="text-2xl font-bold text-white mb-4">
              Custom Training Solutions
            </h3>
            <p className="text-agency-white-muted mb-6">
              Need specialized training for your team or organization? We offer custom workshops and training programs tailored to your specific needs.
            </p>
            <Button 
              variant="outline" 
              className="border-agency-green text-agency-green hover:bg-agency-green hover:text-agency-dark"
              onClick={() => window.open('/contact', '_blank')}
            >
              Get Custom Training Quote
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default EducationalServicesSection
